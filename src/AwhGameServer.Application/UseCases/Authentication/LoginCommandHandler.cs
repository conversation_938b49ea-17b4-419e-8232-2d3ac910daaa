using AwhGameServer.Domain.Aggregates.Game;
using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Application.Abstractions.Messaging;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Application.Abstractions.Models;

using AwhGameServer.Application.Dto;

namespace AwhGameServer.Application.UseCases.Authentication;

/// <summary>
/// Обработчик команды авторизации пользователя.
/// </summary>
/// <remarks>
/// Реализует сценарий входа в систему с поддержкой гостевых аккаунтов и их последующей привязки
/// к постоянным методам аутентификации (Google, Apple, соцсети и т.д.).
/// 
/// Логика работы:
/// <list type="number">
/// <item>
/// Если предоставлена не-гостевая точка входа — авторизация выполняется через неё (приоритетнее гостевой).
/// При отсутствии пользователя, но наличии гостевой точки входа — новая точка входа привязывается к существующему пользователю.
/// </item>
/// <item>
/// Если предоставлена только гостевая точка входа — выполняется авторизация или создаётся новый пользователь.
/// </item>
/// <item>
/// Если точек входа нет или они конфликтуют (несколько гостевых или несколько не-гостевых) — выбрасывается исключение.
/// </item>
/// </list>
/// 
/// После успешной аутентификации:
/// <list type="bullet">
/// <item>Генерируется access/refresh токен.</item>
/// <item>Создаётся новая сессия, при этом все предыдущие сессии пользователя аннулируются.</item>
/// </list>
/// 
/// Проверка доступности методов аутентификации выполняется через <see cref="AuthMethodsConfig"/>.
/// </remarks>
public class LoginCommandHandler(
    IAuthenticationUow uow,
    ITypedIdGenerator<UserId> userIdGenerator,
    ITypedIdGenerator<AuthIdentityId> authIdentityIdGenerator,
    IAuthTokensGenerator authTokensGenerator,
    ITokenHasher tokenHasher,
    IAuthSessionStore authSessionStore) 
    : ICommandHandler<LoginCommand, LoginCommandResult>
{
    private const string GuestAuthMethodKey = "Guest";
    
    public async Task<LoginCommandResult> Handle(LoginCommand command, CancellationToken ct)
    {
        if (command.AuthIdentities.Count == 0)
            throw new ArgumentException("AuthIdentities cannot be empty", nameof(command.AuthIdentities));
        
        var nonGuestAuthIdentityDto = GetNonGuestIdentity(command);
        var guestAuthIdentityDto = GetGuestIdentity(command);

        var authMethodsConfig = await uow.AuthMethodsConfigRepository.GetConfigAsync(ct);
        
        AuthTokens authTokens;

        if (nonGuestAuthIdentityDto is not null) // Если есть не_гостевая точка входа, пытаемся авторизоваться через неё (приоритетнее)
            authTokens = await AuthenticateNonGuest(nonGuestAuthIdentityDto, guestAuthIdentityDto, authMethodsConfig, ct);
        else if (guestAuthIdentityDto is not null) // Если есть только гостевая точка входа, пытаемся авторизоваться через неё
            authTokens = await AuthenticateGuest(guestAuthIdentityDto, authMethodsConfig, ct);
        else
            // Если нет ни гостевой, ни не_гостевой точки входа, то выбрасываем исключение (ситуация не должна быть возможной)
            throw new ArgumentException("No valid identities provided", nameof(command.AuthIdentities));

        await uow.SaveChangesAsync(ct);
        
        var authTokensDto = new AuthTokensDto(authTokens.AccessToken, authTokens.RefreshToken);
        
        return new LoginCommandResult("UserId", authTokensDto);
    }
    
    private static AuthIdentityDto? GetNonGuestIdentity(LoginCommand command)
    { 
        var nonGuestIdentities = command.AuthIdentities
            .Where(x => x.AuthMethod != GuestAuthMethodKey)
            .ToList();
        
        //Проверка конфликта нескольких точек входа
        if (nonGuestIdentities.Count > 1)
            throw new ArgumentException("Multiple non-guest identities are not allowed", nameof(command.AuthIdentities));
        
        return nonGuestIdentities.FirstOrDefault();
    }
    
    private static AuthIdentityDto? GetGuestIdentity(LoginCommand command)
    {
        var guestIdentities = command.AuthIdentities
            .Where(x => x.AuthMethod == GuestAuthMethodKey)
            .ToList();
        
        //Проверка конфликта нескольких точек входа
        if (guestIdentities.Count > 1)
            throw new ArgumentException("Multiple guest identities are not allowed", nameof(command.AuthIdentities));
        
        return guestIdentities.FirstOrDefault();
    }
    
    // Пайплайн авторизации через не_гостевую точку входа
    private async Task<AuthTokens> AuthenticateNonGuest(
        AuthIdentityDto nonGuestAuthIdentityDto, 
        AuthIdentityDto? guestAuthIdentityDto, 
        AuthMethodsConfig authMethodsConfig,
        CancellationToken ct)
    {
        var isLoginAllowed = authMethodsConfig.IsLoginMethodAllowed(nonGuestAuthIdentityDto.AuthMethod);
        
        if (!isLoginAllowed)
            throw new ArgumentException($"Login with {nonGuestAuthIdentityDto.AuthMethod} is not allowed");
        
        var userAuthIdentity = await uow.AuthIdentityRepository
            .GetByAuthMethodAndTokenAsync(nonGuestAuthIdentityDto.AuthMethod, nonGuestAuthIdentityDto.AuthToken, ct);

        if (userAuthIdentity is not null)
            // Если пользователь уже есть, то авторизуем его
            return await Authenticate(userAuthIdentity, ct);

        // Если пользователя нет, то проверяем, есть ли гостевая точка входа
        if (guestAuthIdentityDto is not null)
        {
            var guestAuthIdentity = await uow.AuthIdentityRepository
                .GetByAuthMethodAndTokenAsync(guestAuthIdentityDto.AuthMethod, guestAuthIdentityDto.AuthToken, ct);

            if (guestAuthIdentity is not null)
                // Если есть гостевая точка входа, но нет не_гостевой, то привязываем к нему новую точку входа
                userAuthIdentity = await CreateNewAuthIdentity(guestAuthIdentity.UserId, nonGuestAuthIdentityDto, authMethodsConfig, ct);
        }
        
        if (userAuthIdentity is not null)
            // Если есть гостевая точка входа, то авторизуем его (с новой не_гостевой точкой входа)
            return await Authenticate(userAuthIdentity, ct);
        
        // Если нет гостевой точки входа, то создаём нового пользователя
        userAuthIdentity = await CreateNewUser(nonGuestAuthIdentityDto, authMethodsConfig, ct);

        // Авторизуем нового пользователя
        return await Authenticate(userAuthIdentity, ct);
    }
    
    // Пайплайн авторизации через гостевую точку входа
    private async Task<AuthTokens> AuthenticateGuest(
        AuthIdentityDto guestIdentity, 
        AuthMethodsConfig authMethodsConfig, 
        CancellationToken ct)
    {
        var isLoginAllowed = authMethodsConfig.IsLoginMethodAllowed(GuestAuthMethodKey);
        
        if (!isLoginAllowed)
            throw new ArgumentException($"Login with {GuestAuthMethodKey} is not allowed");
        
        var authIdentity = await uow.AuthIdentityRepository
            .GetByAuthMethodAndTokenAsync(guestIdentity.AuthMethod, guestIdentity.AuthToken, ct);
        
        // Если гостевая точка входа не найдена, то создаём нового пользователя
        authIdentity ??= await CreateNewUser(guestIdentity, authMethodsConfig, ct);
        
        return await Authenticate(authIdentity, ct);
    }

    private async Task<AuthIdentity> CreateNewUser(
        AuthIdentityDto authIdentityDto,  
        AuthMethodsConfig authMethodsConfig, 
        CancellationToken ct)
    {
        var userId = await userIdGenerator.New();

        return await CreateNewAuthIdentity(userId, authIdentityDto, authMethodsConfig, ct);
    }

    private async Task<AuthIdentity> CreateNewAuthIdentity(
        UserId userId, 
        AuthIdentityDto authIdentityDto,
        AuthMethodsConfig authMethodsConfig,
        CancellationToken ct)
    {
        var isRegistrationAllowed = authMethodsConfig.IsRegistrationMethodAllowed(authIdentityDto.AuthMethod);
        
        if (!isRegistrationAllowed)
            throw new ArgumentException($"Registration with {authIdentityDto.AuthMethod} is not allowed");
        
        var authIdentityId = await authIdentityIdGenerator.New();
        
        var authIdentity = new AuthIdentity(authIdentityId, userId, authIdentityDto.AuthToken, authIdentityDto.AuthMethod);
            
        await uow.AuthIdentityRepository.AddAsync(authIdentity, ct);
        
        return authIdentity;
    }

    private async Task<AuthTokens> Authenticate(AuthIdentity authIdentity, CancellationToken ct)
    {
        var sessionId = Guid.NewGuid().ToString();
        
        var authTokens = await authTokensGenerator.GenerateAuthTokens(sessionId, authIdentity.UserId, ct);
        
        var refreshHash = await tokenHasher.HashToken(authTokens.RefreshToken, ct);
        
        await authSessionStore.RevokeAllUserSessionsThenCreateAsync(
            sessionId, 
            authIdentity.UserId, 
            refreshHash, 
            authIdentity.Id, 
            authTokens.RefreshTokenExpiresAtUtc, // Создаем сессию на то же время, что и refresh token
            ct);
        
        return authTokens;
    }
}
