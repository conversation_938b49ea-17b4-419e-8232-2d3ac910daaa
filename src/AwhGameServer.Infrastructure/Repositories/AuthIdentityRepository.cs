using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Infrastructure.Persistence.Ef;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.UsersData;

namespace AwhGameServer.Infrastructure.Repositories;

/// <summary>
/// Реализация репозитория для работы с точками входа в пользовательский аккаунт.
/// Обеспечивает доступ к данным <see cref="AuthIdentity"/> через Entity Framework и MongoDB.
/// </summary>
/// <param name="context">Контекст базы пользовательских данных.</param>
/// <param name="logger">Логгер для записи информации о работе репозитория.</param>
public class AuthIdentityRepository(UsersDataDbContext context, ILogger<AuthIdentityRepository> logger) : IAuthIdentityRepository
{
    /// <inheritdoc />
    public async Task<AuthIdentity?> GetByIdAsync(AuthIdentityId authIdentityId, CancellationToken ct)
    {
        logger.LogDebug("Поиск точки входа по ID: {AuthIdentityId}", authIdentityId.Value);

        if (!ObjectId.TryParse(authIdentityId.Value, out var objectId))
        {
            logger.LogWarning("Некорректный формат ID точки входа: {AuthIdentityId}", authIdentityId.Value);
            return null;
        }

        var document = await context.AuthIdentities
            .FirstOrDefaultAsync(x => x.Id == objectId, ct);

        var result = document == null ? null : MapToDomain(document);

        if (result != null)
        {
            logger.LogDebug("Найдена точка входа {AuthIdentityId} для пользователя {UserId}",
                authIdentityId.Value, result.UserId.Value);
        }
        else
        {
            logger.LogDebug("Точка входа с ID {AuthIdentityId} не найдена", authIdentityId.Value);
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<AuthIdentity?> GetByAuthMethodAndTokenAsync(string authMethod, string authToken, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Поиск точки входа по методу аутентификации: {AuthMethod}", authMethod);

        var document = await context.AuthIdentities
            .FirstOrDefaultAsync(x => x.AuthMethod == authMethod && x.AuthToken == authToken, cancellationToken);

        var result = document == null ? null : MapToDomain(document);

        if (result != null)
        {
            logger.LogDebug("Найдена точка входа {AuthIdentityId} для метода {AuthMethod} и пользователя {UserId}",
                result.Id.Value, authMethod, result.UserId.Value);
        }
        else
        {
            logger.LogDebug("Точка входа для метода {AuthMethod} не найдена", authMethod);
        }

        return result;
    }

    /// <inheritdoc />
    public async Task<IReadOnlyCollection<AuthIdentity>> GetByUserIdAsync(UserId userId, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Поиск всех точек входа для пользователя: {UserId}", userId.Value);

        var documents = await context.AuthIdentities
            .Where(x => x.UserId == userId.Value)
            .ToListAsync(cancellationToken);

        var result = documents.Select(MapToDomain).ToList();

        logger.LogDebug("Найдено {Count} точек входа для пользователя {UserId}", result.Count, userId.Value);

        return result;
    }

    /// <inheritdoc />
    public async Task AddAsync(AuthIdentity authIdentity, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Добавление новой точки входа для пользователя {UserId} с методом {AuthMethod}",
            authIdentity.UserId.Value, authIdentity.AuthMethod);

        var document = MapToDocument(authIdentity);
        await context.AuthIdentities.AddAsync(document, cancellationToken);

        logger.LogDebug("Точка входа {AuthIdentityId} успешно добавлена", authIdentity.Id.Value);
    }

    /// <summary>
    /// Преобразует документ MongoDB в доменную модель.
    /// </summary>
    /// <param name="document">Документ из базы данных.</param>
    /// <returns>Доменная модель точки входа.</returns>
    private static AuthIdentity MapToDomain(AuthIdentityDocument document)
    {
        return new AuthIdentity(
            new AuthIdentityId(document.Id.ToString()),
            new UserId(document.UserId),
            document.AuthToken,
            document.AuthMethod
        );
    }

    /// <summary>
    /// Преобразует доменную модель в документ MongoDB.
    /// </summary>
    /// <param name="authIdentity">Доменная модель точки входа.</param>
    /// <returns>Документ для сохранения в базе данных.</returns>
    private static AuthIdentityDocument MapToDocument(AuthIdentity authIdentity)
    {
        var document = new AuthIdentityDocument
        {
            UserId = authIdentity.UserId.Value,
            AuthMethod = authIdentity.AuthMethod,
            AuthToken = authIdentity.AuthToken
        };

        if (ObjectId.TryParse(authIdentity.Id.Value, out var objectId))
        {
            document.Id = objectId;
        }

        return document;
    }
}
