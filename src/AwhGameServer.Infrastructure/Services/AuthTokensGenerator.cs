using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Infrastructure.Configurations;

namespace AwhGameServer.Infrastructure.Services;

/// <summary>
/// Сервис для генерации токенов аутентификации (access и refresh токенов).
/// </summary>
/// <remarks>
/// Генерирует JWT access токены с подписью HMAC-SHA256 и криптографически стойкие refresh токены.
/// Access токены содержат claims с информацией о пользователе и сессии.
/// Refresh токены представляют собой случайные 256-битные значения в формате Base64Url.
///
/// Время жизни токенов настраивается через <see cref="AuthSessionConfig"/>.
/// </remarks>
public class AuthTokensGenerator(IOptions<AuthSessionConfig> config, ILogger<AuthTokensGenerator> logger) : IAuthTokensGenerator
{
    private readonly AuthSessionConfig _config = config.Value;
    private static readonly JwtSecurityTokenHandler JwtSecurityTokenHandler = new();

    /// <summary>
    /// Генерирует пару токенов аутентификации (access и refresh) для указанной сессии и пользователя.
    /// </summary>
    /// <param name="sessionId">Идентификатор сессии пользователя.</param>
    /// <param name="userId">Идентификатор пользователя.</param>
    /// <param name="ct">Токен отмены операции.</param>
    /// <returns>
    /// Объект <see cref="AuthTokens"/> содержащий access токен, refresh токен и время их истечения.
    /// </returns>
    /// <remarks>
    /// Access токен представляет собой JWT с claims:
    /// - sub: идентификатор пользователя
    /// - sid: идентификатор сессии
    /// - jti: уникальный идентификатор токена
    /// - iat: время выдачи токена
    ///
    /// Refresh токен представляет собой криптографически стойкую случайную строку длиной 256 бит.
    /// </remarks>
    public Task<AuthTokens> GenerateAuthTokens(string sessionId, UserId userId, CancellationToken ct = default)
    {
        logger.LogDebug("Генерация токенов аутентификации для пользователя {UserId} и сессии {SessionId}",
            userId.Value, sessionId);

        var now = DateTimeOffset.UtcNow;

        var accessToken = GenerateAccessToken(sessionId, userId, now);
        var refreshToken = GenerateRefreshToken();

        var accessExp = now.AddMinutes(_config.AccessTokenLifetimeMinutes);
        var refreshExp = now.AddDays(_config.RefreshTokenLifetimeDays);

        logger.LogDebug("Токены успешно сгенерированы для пользователя {UserId}. Access токен истекает: {AccessExpiry}, Refresh токен истекает: {RefreshExpiry}",
            userId.Value, accessExp, refreshExp);

        return Task.FromResult(new AuthTokens(
            accessToken,
            refreshToken,
            accessExp.UtcDateTime,
            refreshExp.UtcDateTime));
    }

    /// <summary>
    /// Генерирует JWT access токен с подписью HMAC-SHA256.
    /// </summary>
    /// <param name="sessionId">Идентификатор сессии для включения в claim 'sid'.</param>
    /// <param name="userId">Идентификатор пользователя для включения в claim 'sub'.</param>
    /// <param name="now">Текущее время для установки времени выдачи и истечения токена.</param>
    /// <returns>Подписанный JWT токен в виде строки.</returns>
    private string GenerateAccessToken(string sessionId, UserId userId, DateTimeOffset now)
    {
        var keyBytes = Convert.FromBase64String(_config.AccessTokenSecretBase64);
        var key = new SymmetricSecurityKey(keyBytes);
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        
        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, userId.ToString()),
            new Claim(JwtRegisteredClaimNames.Sid, sessionId),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, now.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
        };
        
        var token = new JwtSecurityToken(
            issuer: _config.AccessTokenIssuer,
            audience: _config.AccessTokenAudience,
            claims: claims,
            notBefore: now.UtcDateTime,
            expires: now.AddMinutes(_config.AccessTokenLifetimeMinutes).UtcDateTime,
            signingCredentials: credentials);

        return JwtSecurityTokenHandler.WriteToken(token);
    }

    /// <summary>
    /// Генерирует криптографически стойкий refresh токен.
    /// </summary>
    /// <returns>
    /// Случайная строка длиной 256 бит в формате Base64Url без padding символов.
    /// </returns>
    /// <remarks>
    /// Использует <see cref="RandomNumberGenerator"/> для генерации криптографически стойких случайных байтов.
    /// Результат кодируется в Base64Url формат для безопасной передачи в URL и HTTP заголовках.
    /// </remarks>
    private static string GenerateRefreshToken()
    {
        Span<byte> bytes = stackalloc byte[32]; // 256 бит
        RandomNumberGenerator.Fill(bytes);
        return Base64UrlEncode(bytes);
    }

    /// <summary>
    /// Кодирует массив байтов в Base64Url формат.
    /// </summary>
    /// <param name="bytes">Байты для кодирования.</param>
    /// <returns>
    /// Строка в формате Base64Url без padding символов ('=') и с заменой символов '+' на '-' и '/' на '_'.
    /// </returns>
    /// <remarks>
    /// Base64Url формат безопасен для использования в URL и HTTP заголовках,
    /// так как не содержит символы, требующие URL-кодирования.
    /// </remarks>
    private static string Base64UrlEncode(ReadOnlySpan<byte> bytes)
    {
        var s = Convert.ToBase64String(bytes);
        s = s.TrimEnd('=').Replace('+', '-').Replace('/', '_');
        return s;
    }
}
